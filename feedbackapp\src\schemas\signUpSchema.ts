import {z} from "zod"


export const usernameValidation = z.string().min(2,"Username must have 2 charecters").max(20,"Must be no more 20 charecters").regex(/^[a-zA-Z0-9]+$/, "Username must not contain special characters")


export const signUpSchema = z.object({
    username : usernameValidation,
    email : z.email({message : "Invalid email address"}),
    password : z.string().min(6,{message : "Password Must contain 6 charecters"})
})
