import mongoose,{Schema,Document, Mongoose}  from "mongoose";

export interface Message extends Document{
    content  :string;
    createdAt:Date
}

const MessageSchema : Schema<Message> = new Schema({
    content : {
        type: String,
        required:true
    },
    createdAt:{
        type: Date,
        required:true,
        default:Date.now
    }
})


export interface User extends Document{
    username  :string;
    email  :string;
    password  :string;
    verifyCode  :string;
    verifyCodeExpiry  :Date;
    isVerified : boolean;
    isAcceptMessage : boolean;
    message: Message[]
}

const UserSchema : Schema<User> = new Schema({
    username:{
        type:String,
        required:true,
        unique:true
    },
    email:{
        type:String,
        required:true,
        match:[/^[\w-]+(\.[\w-]+)*@([\w-]+\.)+[a-zA-Z]{2,7}$/, 'please use a valid email address']
    },
    password:{
        type:String,
        required:true
    },
    verifyCode:{
        type:String,
        required:true
    },
    verifyCodeExpiry:{
        type:Date,
        required:true
    },
    isVerified:{
        type : Boolean,
        default:false
    },
    isAcceptMessage:{
        type : Boolean,
        default:true
    },
    message:[MessageSchema]
}) 

const UserModel = (mongoose.models.User as mongoose.Model<User>) || mongoose.model<User>("User",UserSchema)

export default UserModel;
