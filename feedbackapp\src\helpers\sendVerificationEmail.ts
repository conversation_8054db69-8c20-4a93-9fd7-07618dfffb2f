import { resend } from "@/lib/resend";
import { Verification } from 'next/dist/lib/metadata/types/metadata-types';
import { ApiResponse } from '@/types/ApiResponse';
import VerficationEmail from 
export async function sendVerficationEmail(
    email:string,
    username:string,
    verifyCode:string
):Promise<ApiResponse>{

    try{
        await resend.emails.send({
            from:'<EMAIL>',
            to:email,
            subject:'Verification Code',
            react: VerificaVerficationEmail
        });
        return {success : true,message : "verification email sent successfully"}
    }catch(emailError){
        console.error("Error sending verification email ",emailError)
        return {success : false,message : "failed to send verification email"}
    }

}



